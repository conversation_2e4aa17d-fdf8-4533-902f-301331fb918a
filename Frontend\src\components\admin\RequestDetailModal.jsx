import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentRequestDetail, selectUI, hideRequestDetailModal } from '../../redux/slices/adminDashboardSlice';
import { FaTimes, FaUser, FaCalendarAlt, FaDollarSign, FaFileAlt, FaDumbbell, FaEnvelope, FaPhone } from 'react-icons/fa';
import { getSmartFileUrl, getPlaceholderImage, IMAGE_BASE_URL } from '../../utils/constants';
import '../../styles/RequestDetailModal.css';

const RequestDetailModal = () => {
    const dispatch = useDispatch();
    const currentRequest = useSelector(selectCurrentRequestDetail);
    const ui = useSelector(selectUI);

    if (!currentRequest || !ui.showRequestDetailModal) return null;

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const handleClose = () => {
        dispatch(hideRequestDetailModal());
    };

    const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'pending': return '#f39c12';
            case 'accepted': return '#27ae60';
            case 'rejected': return '#e74c3c';
            case 'in progress': return '#3498db';
            case 'completed': return '#2ecc71';
            case 'cancelled': return '#95a5a6';
            default: return '#7f8c8d';
        }
    };

    return (
        <div className="RequestDetailModal">
            <div className="RequestDetailModal__overlay" onClick={handleClose} />
            <div className="RequestDetailModal__container">
                {/* Header */}
                <div className="RequestDetailModal__header">
                    <div className="header-content">
                        <div className="request-info">
                            <h2>{currentRequest.title || 'Untitled Request'}</h2>
                            <div className="request-meta">
                                <span className="request-id">
                                    #{currentRequest._id?.slice(-8).toUpperCase() || 'N/A'}
                                </span>
                                <span 
                                    className="status-badge"
                                    style={{ backgroundColor: getStatusColor(currentRequest.status) }}
                                >
                                    {currentRequest.status || 'Unknown'}
                                </span>
                            </div>
                        </div>
                    </div>
                    <button className="close-button" onClick={handleClose}>
                        <FaTimes />
                    </button>
                </div>

                {/* Content */}
                <div className="RequestDetailModal__content">
                    {/* Request Details */}
                    <section className="detail-section">
                        <h3>Request Details</h3>
                        <div className="info-grid">
                            <div className="info-item">
                                <FaFileAlt className="icon" />
                                <div>
                                    <label>Content Type:</label>
                                    <span>{currentRequest.contentType || 'N/A'}</span>
                                </div>
                            </div>
                            <div className="info-item">
                                <FaDumbbell className="icon" />
                                <div>
                                    <label>Sport:</label>
                                    <span>{currentRequest.sport || 'N/A'}</span>
                                </div>
                            </div>
                            <div className="info-item">
                                <FaDollarSign className="icon" />
                                <div>
                                    <label>Budget:</label>
                                    <span>{formatCurrency(currentRequest.budget || 0)}</span>
                                </div>
                            </div>
                            <div className="info-item">
                                <FaCalendarAlt className="icon" />
                                <div>
                                    <label>Requested Delivery:</label>
                                    <span>
                                        {currentRequest.requestedDeliveryDate 
                                            ? formatDate(currentRequest.requestedDeliveryDate)
                                            : 'Not specified'}
                                    </span>
                                </div>
                            </div>
                            <div className="info-item">
                                <FaCalendarAlt className="icon" />
                                <div>
                                    <label>Created:</label>
                                    <span>
                                        {currentRequest.createdAt 
                                            ? formatDate(currentRequest.createdAt)
                                            : 'N/A'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Description */}
                    <section className="detail-section">
                        <h3>Description</h3>
                        <div className="description-content">
                            {currentRequest.description || 'No description provided.'}
                        </div>
                    </section>

                    {/* Buyer Details */}
                    <section className="detail-section">
                        <h3>Buyer Information</h3>
                        <div className="user-profile">
                            <div className="user-avatar">
                                <img
                                    src={IMAGE_BASE_URL + currentRequest.buyer?.profileImage || '/default-profile.jpg'}
                                    alt={currentRequest.buyer?.firstName}
                                    className="profile-image"
                                />
                            </div>
                            <div className="user-details">
                                <div className="user-header">
                                    <FaUser className="icon" />
                                    <span className="name">
                                        {currentRequest.buyer?.firstName && currentRequest.buyer?.lastName
                                            ? `${currentRequest.buyer.firstName} ${currentRequest.buyer.lastName}`
                                            : 'Unknown User'}
                                    </span>
                                </div>
                                <div className="user-contact">
                                    <div className="contact-item">
                                        <FaEnvelope className="icon" />
                                        <span>{currentRequest.buyer?.email || 'No email'}</span>
                                    </div>
                                    <div className="contact-item">
                                        <FaPhone className="icon" />
                                        <span>{currentRequest.buyer?.mobile || 'No phone'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Seller Details (if assigned) */}
                    {currentRequest.seller && (
                        <section className="detail-section">
                            <h3>Seller Information</h3>
                            <div className="user-profile">
                                <div className="user-avatar">
                                    <img
                                        src={IMAGE_BASE_URL + currentRequest.seller?.profileImage || '/default-profile.jpg'}
                                        alt={currentRequest.seller?.firstName}
                                        className="profile-image"
                                    />
                                </div>
                                <div className="user-details">
                                    <div className="user-header">
                                        <FaUser className="icon" />
                                        <span className="name">
                                            {currentRequest.seller?.firstName && currentRequest.seller?.lastName
                                                ? `${currentRequest.seller.firstName} ${currentRequest.seller.lastName}`
                                                : 'Unknown User'}
                                        </span>
                                    </div>
                                    <div className="user-contact">
                                        <div className="contact-item">
                                            <FaEnvelope className="icon" />
                                            <span>{currentRequest.seller?.email || 'No email'}</span>
                                        </div>
                                        <div className="contact-item">
                                            <FaPhone className="icon" />
                                            <span>{currentRequest.seller?.mobile || 'No phone'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    )}

                    {/* Seller Response (if available) */}
                    {currentRequest.sellerResponse && currentRequest.sellerResponse.accepted !== undefined && (
                        <section className="detail-section">
                            <h3>Seller Response</h3>
                            <div className="seller-response">
                                <div className="response-status">
                                    <strong>Response: </strong>
                                    <span className={currentRequest.sellerResponse.accepted ? 'accepted' : 'declined'}>
                                        {currentRequest.sellerResponse.accepted ? 'Accepted' : 'Declined'}
                                    </span>
                                </div>
                                {currentRequest.sellerResponse.price && (
                                    <div className="response-price">
                                        <strong>Quoted Price: </strong>
                                        {formatCurrency(currentRequest.sellerResponse.price)}
                                    </div>
                                )}
                                {currentRequest.sellerResponse.estimatedDeliveryDate && (
                                    <div className="response-delivery">
                                        <strong>Estimated Delivery: </strong>
                                        {formatDate(currentRequest.sellerResponse.estimatedDeliveryDate)}
                                    </div>
                                )}
                                {currentRequest.sellerResponse.message && (
                                    <div className="response-message">
                                        <strong>Message: </strong>
                                        <p>{currentRequest.sellerResponse.message}</p>
                                    </div>
                                )}
                            </div>
                        </section>
                    )}
                </div>
            </div>
        </div>
    );
};

export default RequestDetailModal;
