

.AdminRequestManagement .AdminRequestManagement__header {
  margin-bottom: 0.5rem;
}

.AdminRequestManagement .AdminRequestManagement__header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.AdminRequestManagement .AdminRequestManagement__header p {
  color: #666;
  font-size: 1rem;
}

.AdminRequestManagement .AdminRequestManagement__controls {
  display: flex;
 
  align-items: center;
  margin-bottom: 24px;
  gap: 24px;
}

.AdminRequestManagement .search-box {
  flex: 1;
  position: relative;
  max-width: 400px;
}

.AdminRequestManagement .search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
    font-size: 12px;
}

.AdminRequestManagement .search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.AdminRequestManagement .filter-box {
  display: flex;
  gap: 1rem;
}

.AdminRequestManagement .filter-box select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  min-width: 150px;
}

.AdminRequestManagement .AdminRequestManagement__table {
  background: white;
  border-radius: 8px;
border: 1px solid var(--light-gray);
  overflow: hidden;
  margin-bottom: 2rem;
}

.AdminRequestManagement .AdminRequestManagement__table table {
  width: 100%;
  border-collapse: collapse;
}

.AdminRequestManagement .AdminRequestManagement__table th,
.AdminRequestManagement .AdminRequestManagement__table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.AdminRequestManagement .AdminRequestManagement__table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.AdminRequestManagement .AdminRequestManagement__table tr:hover {
  background: #f8f9fa;
}

.AdminRequestManagement .AdminRequestManagement__table .no-data {
  text-align: center;
  color: #666;
  padding: 2rem;
}

/* Pagination Styles */
.AdminRequestManagement .AdminRequestManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont) 0;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  margin-top: var(--basefont);
  border: 1px solid var(--light-gray);
}

.AdminRequestManagement .pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  padding-left: var(--basefont);
}

.AdminRequestManagement .pagination-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Request specific styles */
.AdminRequestManagement .request-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #6366f1;
}

.AdminRequestManagement .request-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.AdminRequestManagement .request-title {
  font-weight: 600;
  color: #1f2937;
}

.AdminRequestManagement .content-type-badge {
  display: inline-block;
  padding: 2px 8px;
  background: #e5e7eb;
  color: #374151;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.AdminRequestManagement .user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.AdminRequestManagement .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.AdminRequestManagement .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.AdminRequestManagement .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}

.AdminRequestManagement .user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AdminRequestManagement .user-details .name {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.AdminRequestManagement .user-details .email {
  color: #6b7280;
  font-size: 0.75rem;
}

.AdminRequestManagement .sport-badge {
  display: inline-block;
  padding: 4px 12px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
}

.AdminRequestManagement .budget-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #059669;
  font-weight: 600;
}

.AdminRequestManagement .budget-info .icon {
  font-size: 0.875rem;
}

.AdminRequestManagement .status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.AdminRequestManagement .status-badge.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.AdminRequestManagement .status-badge.status-accepted {
  background: #d1fae5;
  color: #065f46;
}

.AdminRequestManagement .status-badge.status-rejected {
  background: #fee2e2;
  color: #991b1b;
}

.AdminRequestManagement .status-badge.status-completed {
  background: #dcfce7;
  color: #166534;
}

.AdminRequestManagement .status-badge.status-cancelled {
  background: #f3f4f6;
  color: #374151;
}

.AdminRequestManagement .date-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 0.875rem;
}

.AdminRequestManagement .date-info .icon {
  font-size: 0.75rem;
}

/* Action buttons */
.AdminRequestManagement .action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
}

.AdminRequestManagement .btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.AdminRequestManagement .btn-sm {
  padding: 4px 8px;
  font-size: 11px;
  min-width: 28px;
  height: 28px;
}

.AdminRequestManagement .btn-primary {
  background: #3b82f6;
  color: white;
}

.AdminRequestManagement .btn-primary:hover {
  background: #2563eb;
}

.AdminRequestManagement .btn-success {
  background: #10b981;
  color: white;
}

.AdminRequestManagement .btn-success:hover {
  background: #059669;
}

.AdminRequestManagement .btn-danger {
  background: #ef4444;
  color: white;
}

.AdminRequestManagement .btn-danger:hover {
  background: #dc2626;
}

/* No results styling */
.AdminRequestManagement .no-results {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.AdminRequestManagement .no-results-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.AdminRequestManagement .no-results h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 1.25rem;
}

.AdminRequestManagement .no-results p {
  margin: 0;
  font-size: 0.875rem;
}

/* Pagination buttons - Previous/Next */
.AdminRequestManagement .pagination-controls .btn,
.AdminRequestManagement .pagination-controls button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 80px !important;
  height: 32px !important;
  padding: 6px 12px !important;
  border: 1px solid #dee2e6 !important;
  border-radius: var(--border-radius) !important;
  font-size: var(--smallfont) !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  background-color: var(--white) !important;
  color: #495057 !important;
  text-decoration: none !important;
  outline: none !important;
}

.AdminRequestManagement .pagination-controls .btn:hover:not(:disabled),
.AdminRequestManagement .pagination-controls button:hover:not(:disabled) {
  background-color: var(--btn-color) !important;
  border-color: var(--btn-color) !important;
  color: var(--white) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(238, 52, 37, 0.2);
}

.AdminRequestManagement .pagination-controls .btn:disabled,
.AdminRequestManagement .pagination-controls button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  border-color: #dee2e6 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Page number display */
.AdminRequestManagement .page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--btn-color);
}

/* Status badges */
.AdminRequestManagement .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.AdminRequestManagement .status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.AdminRequestManagement .status-badge.approved {
  background: #d4edda;
  color: #155724;
}

.AdminRequestManagement .status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.AdminRequestManagement .status-badge.completed {
  background: #cce5ff;
  color: #004085;
}

/* Action buttons */
.AdminRequestManagement .action-buttons {
  display: flex;
  gap: 0.5rem;
}

.AdminRequestManagement .action-button {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminRequestManagement .action-button.view {
  background: #007bff;
}

.AdminRequestManagement .action-button.edit {
  background: #28a745;
}

.AdminRequestManagement .action-button.delete {
  background: #dc3545;
}

.AdminRequestManagement .action-button:hover {
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Pagination responsive styles */
  .AdminRequestManagement .AdminRequestManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
    padding: var(--smallfont);
  }

  .AdminRequestManagement .pagination-info {
    padding-left: 0;
    text-align: center;
  }

  .AdminRequestManagement .pagination-controls {
    padding-right: 0;
    justify-content: center;
  }

  .AdminRequestManagement .pagination-controls .btn,
  .AdminRequestManagement .pagination-controls button {
    min-width: 70px !important;
    font-size: var(--extrasmallfont) !important;
    height: 28px !important;
    padding: 4px 8px !important;
  }

  .AdminRequestManagement .AdminRequestManagement__controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .AdminRequestManagement .search-box {
    max-width: 100%;
    width: 100%;
  }
}
