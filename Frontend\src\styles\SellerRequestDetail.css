/* Seller Request Detail Styles - Following consistent dashboard pattern */
.request-detail-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
}

/* Header - Following consistent header pattern */

.request-detail-container .back-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  padding: 10px var(--basefont);
  background: var(--bg-gray);
  border: none;
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.request-detail-container .back-btn:hover {
  background: var(--primary-light-color);
  color: var(--primary-color);
  transform: scale(1.02);
}

.request-detail-container .header-actions {
  display: flex;
  gap: var(--extrasmallfont);
}

/* Request Overview - Following consistent overview pattern */
.request-detail-container .request-overview {
  margin-bottom: var(--heading4);
}

.request-detail-container .overview-header {
  margin-bottom: var(--heading5);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.request-detail-container .request-title-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--basefont);
  margin-bottom: var(--smallfont);
  flex-wrap: wrap;
}

.request-detail-container .title-and-status {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  flex: 1;
  min-width: 0;
}

.request-detail-container .header-action-buttons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  flex-wrap: wrap;
}

.request-detail-container .header-action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
  width: fit-content;
}

.request-detail-container .request-title {
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 700;
}

.request-detail-container .request-meta {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.request-detail-container .request-id {
  font-family: monospace;
  background: var(--bg-gray);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-weight: 500;
}

.request-detail-container .request-date {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.request-detail-container .overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--heading5);
}

.request-detail-container .overview-card {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-detail-container .overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.request-detail-container .card-header {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--basefont);
  background: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
}

.request-detail-container .card-icon {
  font-size: var(--heading6);
  color: var(--primary-color);
}

.request-detail-container .card-header h3 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}

.request-detail-container .card-content {
  padding: var(--basefont);
}

/* Buyer Details - Following consistent info display pattern */
.request-detail-container .buyer-details {
  display: flex;
  flex-direction: column;

}

.request-detail-container .buyer-name {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
}
.request-detail-container .buyer-name span {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
}
.request-detail-container .buyer-email {
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}
.request-detail-container .buyer-email span{
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Budget Details - Following consistent data display pattern */
.request-detail-container .budget-details,
.request-detail-container .request-details {
  display: flex;
  flex-direction: column;
 
}

.request-detail-container .budget-item,
.request-detail-container .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--extrasmallfont) 0;
  border-bottom: 1px solid var(--bg-gray);
}

.request-detail-container .budget-item:last-child,
.request-detail-container .detail-item:last-child {
  border-bottom: none;
}

.request-detail-container .budget-item .label,
.request-detail-container .detail-item .label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

.request-detail-container .budget-item .value,
.request-detail-container .detail-item .value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
}

.request-detail-container .budget-item .value.completed {
  color: #2ecc71;
  font-weight: 700;
}

.request-detail-container .budget-item .value.pending {
  color: #f39c12;
  font-weight: 700;
}

/* Status Badge - Following consistent badge pattern */
.request-detail-container .status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.request-detail-container .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.request-detail-container .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.request-detail-container .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.request-detail-container .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.request-detail-container .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.request-detail-container .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Request Sections - Following consistent section pattern */
.request-detail-container .request-section {
  margin-bottom: var(--heading4);
  padding: var(--heading5);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-detail-container .request-section:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.request-detail-container .section-title {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--extrasmallfont);
}

.request-detail-container .description-content {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  margin-top: var(--basefont);
}

.request-detail-container .description-content p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  line-height: 1.6;
}

/* Response Content - Following consistent content pattern */
.request-detail-container .response-content,
.request-detail-container .submission-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.request-detail-container .response-status,
.request-detail-container .submission-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--extrasmallfont) 0;
  border-bottom: 1px solid var(--light-gray);
}

.request-detail-container .response-badge,
.request-detail-container .submission-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
}

.request-detail-container .response-badge.accepted,
.request-detail-container .submission-badge {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.request-detail-container .response-badge.rejected {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.request-detail-container .response-date,
.request-detail-container .submission-date {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.request-detail-container .response-message,
.request-detail-container .submission-message {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.request-detail-container .response-message h4,
.request-detail-container .submission-message h4 {
  margin: 0 0 var(--extrasmallfont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}

.request-detail-container .response-message p,
.request-detail-container .submission-message p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.5;
}



/* Empty State - Following consistent empty state pattern */
.request-detail-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.request-detail-container .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.request-detail-container .empty-state h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.request-detail-container .empty-state p {
  margin: 0 0 var(--heading5) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .request-detail-container .overview-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }
}

@media (max-width: 768px) {
  .request-detail-container .header-actions {
    justify-content: center;
  }

  .request-detail-container .request-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--basefont);
  }

  .request-detail-container .title-and-status {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-detail-container .header-action-buttons {
    width: 100%;
    justify-content: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-detail-container .header-action-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .request-detail-container .request-title {
    font-size: var(--heading5);
  }

  .request-detail-container .request-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-detail-container .budget-item,
  .request-detail-container .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
    padding: var(--smallfont) 0;
  }

  .request-detail-container .response-status,
  .request-detail-container .submission-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-detail-container .request-section {
    padding: var(--basefont);
  }

  .request-detail-container .card-content {
    padding: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .request-detail-container .back-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  .request-detail-container .request-title {
    font-size: var(--heading5);
  }

  .request-detail-container .overview-grid {
    gap: var(--smallfont);
  }

  .request-detail-container .header-action-buttons {
    flex-direction: column;
    width: 100%;
    align-items: stretch;
  }

  .request-detail-container .header-action-btn {
    justify-content: center;
    text-align: center;
  }
}
